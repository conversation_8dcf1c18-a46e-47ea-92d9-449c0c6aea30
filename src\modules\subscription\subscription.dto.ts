import { IsString, IsEmail, IsDateString, IsNumber, IsEnum, IsOptional } from 'class-validator';
import { SubscriptionStatus } from './subscription.model';

export class CreateSubscriptionDTO {
  @IsEmail()
  userEmail: string;

  @IsString()
  channel: string;

  @IsDateString()
  startDate: string;

  @IsDateString()
  expiryDate: string;

  @IsNumber()
  revenue: number;
}

export class UpdateSubscriptionDTO {
  @IsEnum(SubscriptionStatus)
  @IsOptional()
  status?: SubscriptionStatus;

  @IsDateString()
  @IsOptional()
  expiryDate?: string;

  @IsNumber()
  @IsOptional()
  revenue?: number;
}

export class SubscriptionResponseDTO {
  userEmail: string;
  channel: string;
  startDate: Date;
  expiryDate: Date;
  revenue: number;
  status: SubscriptionStatus;
  id: string;
  created_at: Date;
}