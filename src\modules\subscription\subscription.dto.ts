import { IsString, IsEmail, IsDateString, IsNumber, IsEnum, IsOptional } from 'class-validator';
import { Transform } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { SubscriptionStatus } from './subscription.model';

export class CreateSubscriptionDTO {
  @IsEmail()
  user_email: string;

  @IsString()
  channel: string;

  @IsDateString()
  start_date: string;

  @IsDateString()
  expiry_date: string;

  @IsNumber()
  revenue: number;
}

export class UpdateSubscriptionDTO {
  @IsEnum(SubscriptionStatus)
  @IsOptional()
  status?: SubscriptionStatus;

  @IsDateString()
  @IsOptional()
  expiry_date?: string;

  @IsNumber()
  @IsOptional()
  revenue?: number;
}

export class SubscriptionResponseDTO {
  @Transform(({ obj }) => obj.user_email, { toPlainOnly: true })
  userEmail: string;

  channel: string;

  @Transform(({ obj }) => obj.start_date, { toPlainOnly: true })
  startDate: Date;

  @Transform(({ obj }) => obj.expiry_date, { toPlainOnly: true })
  expiryDate: Date;

  revenue: number;
  status: SubscriptionStatus;
  id: string;

  @Transform(({ obj }) => obj.created_at, { toPlainOnly: true })
  createdAt: Date;
}

export class SubscriptionQueryDTO {
  @ApiProperty({ required: false, description: 'Search keyword for user email or channel' })
  @IsOptional()
  @IsString()
  keyword?: string;

  @ApiProperty({ required: false, description: 'Filter by user email (camelCase for API compatibility)' })
  @IsOptional()
  @IsEmail()
  userEmail?: string;

  @ApiProperty({ required: false, description: 'Filter by channel' })
  @IsOptional()
  @IsString()
  channel?: string;

  @ApiProperty({ required: false, description: 'Filter by status' })
  @IsOptional()
  @IsEnum(SubscriptionStatus)
  status?: SubscriptionStatus;

  @ApiProperty({ required: false, description: 'Page number' })
  @IsOptional()
  @IsNumber()
  page?: number;

  @ApiProperty({ required: false, description: 'Items per page' })
  @IsOptional()
  @IsNumber()
  per_page?: number;

  @ApiProperty({
    required: false,
    description: 'Sort field (supports camelCase: userEmail, startDate, expiryDate, createdAt)',
    example: 'createdAt'
  })
  @IsOptional()
  @IsString()
  sort?: string;
}