import { IsString, IsEmail, IsDate<PERSON>tring, <PERSON><PERSON><PERSON>ber, IsEnum, IsOptional } from 'class-validator';
import { IntersectionType } from '@nestjs/mapped-types';
import { KeywordQueryDTO } from '@app/models/query.model';
import { PaginateOptionWithHotSortDTO } from '@app/models/paginate.model';
import { SubscriptionStatus } from './subscription.model';

export class CreateSubscriptionDTO {
  @IsEmail()
  email: string;

  @IsString()
  channel: string;

  @IsDateString()
  start_date: string;

  @IsDateString()
  expiry_date: string;

  @IsNumber()
  revenue: number;
}

export class UpdateSubscriptionDTO {
  @IsEnum(SubscriptionStatus)
  @IsOptional()
  status?: SubscriptionStatus;

  @IsDateString()
  @IsOptional()
  expiry_date?: string;

  @IsNumber()
  @IsOptional()
  revenue?: number;
}

export class SubscriptionResponseDTO {
  email: string;
  channel: string;
  start_date: Date;
  expiry_date: Date;
  revenue: number;
  status: SubscriptionStatus;
  id: string;
  created_at: Date;
}

export class SubscriptionPaginateQueryDTO extends IntersectionType(PaginateOptionWithHotSortDTO, KeywordQueryDTO) { }