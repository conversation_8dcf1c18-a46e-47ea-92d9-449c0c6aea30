import { IsString, IsEmail, IsDateString, IsN<PERSON>ber, IsEnum, IsOptional } from 'class-validator';
import { Transform } from 'class-transformer';
import { SubscriptionStatus } from './subscription.model';

export class CreateSubscriptionDTO {
  @IsEmail()
  user_email: string;

  @IsString()
  channel: string;

  @IsDateString()
  start_date: string;

  @IsDateString()
  expiry_date: string;

  @IsNumber()
  revenue: number;
}

export class UpdateSubscriptionDTO {
  @IsEnum(SubscriptionStatus)
  @IsOptional()
  status?: SubscriptionStatus;

  @IsDateString()
  @IsOptional()
  expiry_date?: string;

  @IsNumber()
  @IsOptional()
  revenue?: number;
}

export class SubscriptionResponseDTO {
  @Transform(({ obj }) => obj.user_email, { toPlainOnly: true })
  userEmail: string;

  channel: string;

  @Transform(({ obj }) => obj.start_date, { toPlainOnly: true })
  startDate: Date;

  @Transform(({ obj }) => obj.expiry_date, { toPlainOnly: true })
  expiryDate: Date;

  revenue: number;
  status: SubscriptionStatus;
  id: string;

  @Transform(({ obj }) => obj.created_at, { toPlainOnly: true })
  createdAt: Date;
}