import { IsString, IsEmail, IsDate<PERSON>tring, IsN<PERSON>ber, IsEnum, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { SubscriptionStatus } from './subscription.model';

export class CreateSubscriptionDTO {
  @IsEmail()
  user_email: string;

  @IsString()
  channel: string;

  @IsDateString()
  start_date: string;

  @IsDateString()
  expiry_date: string;

  @IsNumber()
  revenue: number;
}

export class UpdateSubscriptionDTO {
  @IsEnum(SubscriptionStatus)
  @IsOptional()
  status?: SubscriptionStatus;

  @IsDateString()
  @IsOptional()
  expiry_date?: string;

  @IsNumber()
  @IsOptional()
  revenue?: number;
}

export class SubscriptionResponseDTO {
  user_email: string;
  channel: string;
  start_date: Date;
  expiry_date: Date;
  revenue: number;
  status: SubscriptionStatus;
  id: string;
  created_at: Date;
}

export class SubscriptionQueryDTO {
  @ApiProperty({ required: false, description: 'Search keyword for user email or channel' })
  @IsOptional()
  @IsString()
  keyword?: string;

  @ApiProperty({ required: false, description: 'Filter by user email' })
  @IsOptional()
  @IsEmail()
  user_email?: string;

  @ApiProperty({ required: false, description: 'Filter by channel' })
  @IsOptional()
  @IsString()
  channel?: string;

  @ApiProperty({ required: false, description: 'Filter by status' })
  @IsOptional()
  @IsEnum(SubscriptionStatus)
  status?: SubscriptionStatus;

  @ApiProperty({ required: false, description: 'Page number' })
  @IsOptional()
  @IsNumber()
  page?: number;

  @ApiProperty({ required: false, description: 'Items per page' })
  @IsOptional()
  @IsNumber()
  per_page?: number;

  @ApiProperty({
    required: false,
    description: 'Sort field (use snake_case: user_email, start_date, expiry_date, created_at)',
    example: 'created_at'
  })
  @IsOptional()
  @IsString()
  sort?: string;
}