import lodash from 'lodash';
import { Controller, Get, Post, Put, Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '@app/modules/auth/guards/jwt-auth.guard';
import { Responser } from '@app/decorators/responser.decorator';
import { PaginateResult, PaginateQuery, PaginateOptions } from '@app/utils/paginate'
import { LeadService } from './lead.service';
import { Lead } from './lead.model';
import { CreateLeadDTO, UpdateLeadDTO, LeadQueryDTO } from './lead.dto';

@ApiBearerAuth()
@ApiTags('Leads')
@UseGuards(JwtAuthGuard)
@Controller({ path: 'leads', version: '1' })
export class LeadController {
  constructor(private readonly leadService: LeadService) {}

  @Get()
  @Responser.paginate()
  @Responser.handle('Get leads')
  getLeads(@Query() query: LeadQueryDTO) {
    const { page, per_page, sort, keyword, ...filters } = query
    const paginateQuery: PaginateQuery<Lead> = {}
    const paginateOptions: PaginateOptions = { page, perPage: per_page, sort }

    // Handle keyword search
    if (keyword) {
      const trimmed = lodash.trim(keyword)
      const keywordRegExp = new RegExp(trimmed, 'i')
      paginateQuery.$or = [{ name: keywordRegExp }, { email: keywordRegExp }]
    }

    // Handle other filters (already in snake_case)
    Object.assign(paginateQuery, filters)

    // paginate
    return this.leadService.paginator(paginateQuery, paginateOptions);
  }

  @Post()
  @Responser.handle('Create lead')
  createLead(@Body() dto: CreateLeadDTO) {
    const lead = new Lead();
    Object.assign(lead, dto);
    return this.leadService.create(lead);
  }

  @Put(':id')
  @Responser.handle('Update lead')
  updateLead(@Param('id') id: string, @Body() dto: UpdateLeadDTO) {
    const update: any = { ...dto };
    if (dto.last_contacted) {
      update.last_contacted = new Date(dto.last_contacted);
    }
    return this.leadService.update(id, update);
  }

  @Post(':id/contact')
  @Responser.handle('Mark lead as contacted')
  markContacted(@Param('id') id: string) {
    return this.leadService.markContacted(id);
  }

  @Post(':id/send-offer')
  @Responser.handle('Send lifetime offer email')
  async sendLifetimeOffer(
    @Param('id') id: string,
    @Body() body: { offerUrl: string; unsubscribeUrl: string }
  ) {
    await this.leadService.sendLifetimeOfferToLead(id, body.offerUrl, body.unsubscribeUrl);
    return { message: 'Offer email sent and lead updated.' };
  }
}