# Subscription API

## Field Naming Convention

This module uses **snake_case** for database fields but supports **camelCase** in API requests for client compatibility.

### Database Fields (snake_case)
- `user_email`
- `start_date`
- `expiry_date`
- `created_at`
- `updated_at`

### API Fields (camelCase - supported for filtering/sorting)
- `userEmail`
- `startDate`
- `expiryDate`
- `createdAt`
- `updatedAt`

## API Usage Examples

### 1. Filtering with camelCase
```bash
# Filter by user email (camelCase)
GET /subscriptions?userEmail=<EMAIL>

# Filter by status
GET /subscriptions?status=active

# Search by keyword
GET /subscriptions?keyword=john
```

### 2. Sorting with camelCase
```bash
# Sort by creation date (ascending)
GET /subscriptions?sort=createdAt

# Sort by creation date (descending)
GET /subscriptions?sort=-createdAt

# Sort by user email
GET /subscriptions?sort=userEmail

# Sort by expiry date
GET /subscriptions?sort=expiryDate
```

### 3. Combined filtering and sorting
```bash
GET /subscriptions?status=active&sort=-createdAt&page=1&per_page=10
```

## Field Mapping

The controller automatically maps camelCase API fields to snake_case database fields:

| API Field (camelCase) | Database Field (snake_case) |
|----------------------|----------------------------|
| `userEmail`          | `user_email`               |
| `startDate`          | `start_date`               |
| `expiryDate`         | `expiry_date`              |
| `createdAt`          | `created_at`               |
| `updatedAt`          | `updated_at`               |

## Response Format

API responses use camelCase for consistency with frontend expectations:

```json
{
  "userEmail": "<EMAIL>",
  "channel": "web",
  "startDate": "2024-01-01T00:00:00.000Z",
  "expiryDate": "2024-12-31T23:59:59.999Z",
  "revenue": 99.99,
  "status": "active",
  "id": "507f1f77bcf86cd799439011",
  "createdAt": "2024-01-01T00:00:00.000Z"
}
```
