import lodash from 'lodash';
import { Controller, Get, Post, Put, Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '@app/modules/auth/guards/jwt-auth.guard';
import { Responser } from '@app/decorators/responser.decorator';
import { PaginateResult, PaginateQuery, PaginateOptions } from '@app/utils/paginate'
import { SubscriptionService } from './subscription.service';
import { Subscription } from './subscription.model';
import { CreateSubscriptionDTO, UpdateSubscriptionDTO, SubscriptionQueryDTO } from './subscription.dto';

// Field mapping from camelCase (API) to snake_case (database)
const FIELD_MAPPING = {
  userEmail: 'user_email',
  startDate: 'start_date',
  expiryDate: 'expiry_date',
  createdAt: 'created_at',
  updatedAt: 'updated_at'
} as const;

// Helper function to convert camelCase fields to snake_case for database queries
function mapFieldsToDatabase(obj: any): any {
  if (!obj || typeof obj !== 'object') return obj;

  const mapped: any = {};
  for (const [key, value] of Object.entries(obj)) {
    const dbField = FIELD_MAPPING[key as keyof typeof FIELD_MAPPING] || key;
    mapped[dbField] = value;
  }
  return mapped;
}

// Helper function to convert sort parameter
function mapSortToDatabase(sort: any): any {
  if (!sort) return sort;
  if (typeof sort === 'string') {
    // Handle sort like "userEmail" or "-userEmail"
    const isDesc = sort.startsWith('-');
    const field = isDesc ? sort.substring(1) : sort;
    const dbField = FIELD_MAPPING[field as keyof typeof FIELD_MAPPING] || field;
    return isDesc ? `-${dbField}` : dbField;
  }
  if (typeof sort === 'object') {
    // Handle sort object like { userEmail: 1, startDate: -1 }
    return mapFieldsToDatabase(sort);
  }
  return sort;
}


@ApiBearerAuth()
@ApiTags('Subscriptions')
@UseGuards(JwtAuthGuard)
@Controller({ path: 'subscriptions', version: '1' })
export class SubscriptionController {
  constructor(private readonly subscriptionService: SubscriptionService) {}

  @Get()
  @Responser.paginate()
  @Responser.handle('Get subscriptions')
  getSubscriptions(@Query() query: SubscriptionQueryDTO) {
    const { page, per_page, sort, keyword, ...filters } = query
    const paginateQuery: PaginateQuery<Subscription> = {}

    // Convert sort parameter from camelCase to snake_case
    const mappedSort = mapSortToDatabase(sort)
    const paginateOptions: PaginateOptions = { page, perPage: per_page, sort: mappedSort }

    // Handle keyword search
    if (keyword) {
      const trimmed = lodash.trim(keyword)
      const keywordRegExp = new RegExp(trimmed, 'i')
      paginateQuery.$or = [{ user_email: keywordRegExp }, { channel: keywordRegExp }]
    }

    // Handle other filters (convert camelCase to snake_case)
    const mappedFilters = mapFieldsToDatabase(filters)
    Object.assign(paginateQuery, mappedFilters)

    // paginate
    return this.subscriptionService.paginator(paginateQuery, paginateOptions);
  }

  @Post()
  @Responser.handle('Create subscription')
  createSubscription(@Body() dto: CreateSubscriptionDTO) {
    const subscription = new Subscription();
    Object.assign(subscription, dto, {
      start_date: new Date(dto.start_date),
      expiry_date: new Date(dto.expiry_date),
    });
    return this.subscriptionService.create(subscription);
  }

  @Put(':id')
  @Responser.handle('Update subscription')
  updateSubscription(@Param('id') id: string, @Body() dto: UpdateSubscriptionDTO) {
    const update: any = { ...dto };
    if (dto.expiry_date) {
      update.expiry_date = new Date(dto.expiry_date);
    }
    return this.subscriptionService.update(id, update);
  }

  @Post(':id/revoke')
  @Responser.handle('Revoke subscription')
  revokeSubscription(@Param('id') id: string) {
    return this.subscriptionService.revoke(id);
  }
}