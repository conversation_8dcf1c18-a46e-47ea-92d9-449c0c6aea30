import lodash from 'lodash';
import { Controller, Get, Post, Put, Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '@app/modules/auth/guards/jwt-auth.guard';
import { Responser } from '@app/decorators/responser.decorator';
import { PaginateQuery, PaginateOptions } from '@app/utils/paginate'
import { SubscriptionService } from './subscription.service';
import { Subscription } from './subscription.model';
import { CreateSubscriptionDTO, UpdateSubscriptionDTO, SubscriptionPaginateQueryDTO } from './subscription.dto';




@ApiBearerAuth()
@ApiTags('Subscriptions')
@UseGuards(JwtAuthGuard)
@Controller({ path: 'subscriptions', version: '1' })
export class SubscriptionController {
  constructor(private readonly subscriptionService: SubscriptionService) {}

  @Get()
  @Responser.paginate()
  @Responser.handle('Get subscriptions')
  getSubscriptions(@Query() query: SubscriptionPaginateQueryDTO) {
    const { page, per_page, sort, keyword, ...filters } = query
    const paginateQuery: PaginateQuery<Subscription> = {}
    const paginateOptions: PaginateOptions = { page, perPage: per_page, sort }

    // Handle keyword search
    if (keyword) {
      const trimmed = lodash.trim(keyword)
      const keywordRegExp = new RegExp(trimmed, 'i')
      paginateQuery.$or = [{ email: keywordRegExp }, { channel: keywordRegExp }]
    }

    // Handle other filters (already in snake_case)
    Object.assign(paginateQuery, filters)

    // paginate
    return this.subscriptionService.paginator(paginateQuery, paginateOptions);
  }

  @Post()
  @Responser.handle('Create subscription')
  createSubscription(@Body() dto: CreateSubscriptionDTO) {
    const subscription = new Subscription();
    Object.assign(subscription, dto, {
      start_date: new Date(dto.start_date),
      expiry_date: new Date(dto.expiry_date),
    });
    return this.subscriptionService.create(subscription);
  }

  @Put(':id')
  @Responser.handle('Update subscription')
  updateSubscription(@Param('id') id: string, @Body() dto: UpdateSubscriptionDTO) {
    const update: any = { ...dto };
    if (dto.expiry_date) {
      update.expiry_date = new Date(dto.expiry_date);
    }
    return this.subscriptionService.update(id, update);
  }

  @Post(':id/revoke')
  @Responser.handle('Revoke subscription')
  revokeSubscription(@Param('id') id: string) {
    return this.subscriptionService.revoke(id);
  }
}