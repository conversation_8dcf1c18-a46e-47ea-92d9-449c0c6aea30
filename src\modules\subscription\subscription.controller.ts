import lodash from 'lodash';
import { Controller, Get, Post, Put, Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '@app/modules/auth/guards/jwt-auth.guard';
import { Responser } from '@app/decorators/responser.decorator';
import { PaginateResult, PaginateQuery, PaginateOptions } from '@app/utils/paginate'
import { SubscriptionService } from './subscription.service';
import { Subscription } from './subscription.model';
import { CreateSubscriptionDTO, UpdateSubscriptionDTO } from './subscription.dto';


@ApiBearerAuth()
@ApiTags('Subscriptions')
@UseGuards(JwtAuthGuard)
@Controller({ path: 'subscriptions', version: '1' })
export class SubscriptionController {
  constructor(private readonly subscriptionService: SubscriptionService) {}

  @Get()
  @Responser.paginate()
  @Responser.handle('Get subscriptions')
  getSubscriptions(@Query() query: any) {
    const { page, per_page, sort, ...filters } = query
    const paginateQuery: PaginateQuery<Subscription> = {}
    const paginateOptions: PaginateOptions = { page, perPage: per_page, sort }

    // search
    if (filters.keyword) {
      const trimmed = lodash.trim(filters.keyword)
      const keywordRegExp = new RegExp(trimmed, 'i')
      paginateQuery.$or = [{ first_name: keywordRegExp }, { last_name: keywordRegExp }, { email: keywordRegExp }]
    }

    // paginate
    return this.subscriptionService.paginator(paginateQuery, paginateOptions);
  }

  @Post()
  @Responser.handle('Create subscription')
  createSubscription(@Body() dto: CreateSubscriptionDTO) {
    const subscription = new Subscription();
    Object.assign(subscription, dto, {
      startDate: new Date(dto.startDate),
      expiryDate: new Date(dto.expiryDate),
    });
    return this.subscriptionService.create(subscription);
  }

  @Put(':id')
  @Responser.handle('Update subscription')
  updateSubscription(@Param('id') id: string, @Body() dto: UpdateSubscriptionDTO) {
    const update: any = { ...dto };
    if (dto.expiryDate) {
      update.expiryDate = new Date(dto.expiryDate);
    }
    return this.subscriptionService.update(id, update);
  }

  @Post(':id/revoke')
  @Responser.handle('Revoke subscription')
  revokeSubscription(@Param('id') id: string) {
    return this.subscriptionService.revoke(id);
  }
}