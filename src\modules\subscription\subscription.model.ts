import { getProviderByTypegooseClass } from '@app/transformers/model.transformer';
import { prop, plugin, modelOptions } from '@typegoose/typegoose';
import { mongoosePaginate } from '@app/utils/paginate';
import { BaseModel } from '@app/models/base.model';

export enum SubscriptionStatus {
  ACTIVE = 'active',
  REVOKED = 'revoked',
  EXPIRED = 'expired',
}

@plugin(mongoosePaginate)
@modelOptions({
  schemaOptions: {
    collection: 'subscriptions',
    versionKey: false,
    toJSON: { getters: true },
    toObject: { getters: true },
    timestamps: {
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    }
  }
})
export class Subscription extends BaseModel {
  @prop({ required: true })
  user_email: string;

  @prop({ required: true })
  channel: string;

  @prop({ required: true })
  start_date: Date;

  @prop({ required: true })
  expiry_date: Date;

  @prop({ required: true })
  revenue: number;

  @prop({ enum: SubscriptionStatus, default: SubscriptionStatus.ACTIVE })
  status: SubscriptionStatus;
}
export const SubscriptionProvider = getProviderByTypegooseClass(Subscription);