import { Is<PERSON><PERSON>, <PERSON>E<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ptional, IsDateString, IsN<PERSON><PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { LeadStatus } from './lead.model';

export class CreateLeadDTO {
  @IsString()
  name: string;

  @IsEmail()
  email: string;
}

export class UpdateLeadDTO {
  @IsEnum(LeadStatus)
  @IsOptional()
  status?: LeadStatus;

  @IsDateString()
  @IsOptional()
  last_contacted?: string;
}

export class LeadResponseDTO {
  name: string;
  email: string;
  status: LeadStatus;
  last_contacted?: Date;
  id: string;
  created_at: Date;
}

export class LeadQueryDTO {
  @ApiProperty({ required: false, description: 'Search keyword for name or email' })
  @IsOptional()
  @IsString()
  keyword?: string;

  @ApiProperty({ required: false, description: 'Filter by name' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ required: false, description: 'Filter by email' })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiProperty({ required: false, description: 'Filter by status' })
  @IsOptional()
  @IsEnum(LeadStatus)
  status?: LeadStatus;

  @ApiProperty({ required: false, description: 'Page number' })
  @IsOptional()
  @IsNumber()
  page?: number;

  @ApiProperty({ required: false, description: 'Items per page' })
  @IsOptional()
  @IsNumber()
  per_page?: number;

  @ApiProperty({
    required: false,
    description: 'Sort field (use snake_case: name, email, status, last_contacted, created_at)',
    example: 'created_at'
  })
  @IsOptional()
  @IsString()
  sort?: string;
}