import { IsString, IsEmail, IsEnum, IsOptional, IsDateString } from 'class-validator';
import { LeadStatus } from './lead.model';

export class CreateLeadDTO {
  @IsString()
  name: string;

  @IsEmail()
  email: string;
}

export class UpdateLeadDTO {
  @IsEnum(LeadStatus)
  @IsOptional()
  status?: LeadStatus;

  @IsDateString()
  @IsOptional()
  lastContacted?: string;
}

export class LeadResponseDTO {
  name: string;
  email: string;
  status: LeadStatus;
  createdDate: Date;
  lastContacted?: Date;
  id: string;
  created_at: Date;
}