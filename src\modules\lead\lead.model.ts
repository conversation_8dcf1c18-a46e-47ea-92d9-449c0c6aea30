import { getProviderByTypegooseClass } from '@app/transformers/model.transformer';
import { prop, plugin, modelOptions } from '@typegoose/typegoose';
import { BaseModel } from '@app/models/base.model';
import { mongoosePaginate } from '@app/utils/paginate'

export enum LeadStatus {
  NEW = 'New',
  CONTACTED = 'Contacted',
}

@plugin(mongoosePaginate)
@modelOptions({
  schemaOptions: {
    collection: 'leads',
    versionKey: false,
    toJSON: { getters: true },
    toObject: { getters: true },
    timestamps: {
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    }
  }
})
export class Lead extends BaseModel {
  @prop({ required: true })
  name: string;

  @prop({ required: true, unique: true })
  email: string;

  @prop({ enum: LeadStatus, default: LeadStatus.NEW })
  status: LeadStatus;

  @prop({ default: Date.now })
  createdDate: Date;

  @prop()
  lastContacted?: Date;
}

export const LeadProvider = getProviderByTypegooseClass(Lead);