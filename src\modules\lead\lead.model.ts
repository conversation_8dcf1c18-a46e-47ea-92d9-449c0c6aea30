import { getProviderByTypegooseClass } from '@app/transformers/model.transformer';
import { prop, plugin, modelOptions } from '@typegoose/typegoose';
import { Exclude } from 'class-transformer';
import { BaseModel } from '@app/models/base.model';
import { mongoosePaginate } from '@app/utils/paginate'

export enum LeadStatus {
  NEW = 'New',
  CONTACTED = 'Contacted',
}

@plugin(mongoosePaginate)
@modelOptions({
  schemaOptions: {
    collection: 'leads',
    versionKey: false,
    toJSON: { getters: true },
    toObject: { getters: true },
    timestamps: {
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    }
  }
})
export class Lead extends BaseModel {
  @prop({ required: true })
  name: string;

  @prop({ required: true, unique: true })
  email: string;

  @prop({ enum: LeadStatus, default: LeadStatus.NEW })
  status: LeadStatus;

  @prop()
  last_contacted?: Date;

  @Exclude()
  @prop({ default: Date.now })
  updated_at?: Date
}

export const LeadProvider = getProviderByTypegooseClass(Lead);