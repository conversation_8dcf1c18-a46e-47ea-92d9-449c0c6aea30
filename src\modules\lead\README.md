# Lead API

## Field Naming Convention

This module uses **snake_case** consistently for both database fields and API requests/responses for simplicity.

### Fields (snake_case)
- `name`
- `email`
- `status`
- `last_contacted`
- `created_at`
- `updated_at`

## API Usage Examples

### 1. Filtering with snake_case
```bash
# Filter by name
GET /leads?name=John

# Filter by email
GET /leads?email=<EMAIL>

# Filter by status
GET /leads?status=New

# Search by keyword (searches name and email)
GET /leads?keyword=john
```

### 2. Sorting with snake_case
```bash
# Sort by creation date (ascending)
GET /leads?sort=created_at

# Sort by creation date (descending)
GET /leads?sort=-created_at

# Sort by name
GET /leads?sort=name

# Sort by last contacted date
GET /leads?sort=last_contacted
```

### 3. Combined filtering and sorting
```bash
GET /leads?status=New&sort=-created_at&page=1&per_page=10
```

## Response Format

API responses use snake_case for consistency with database fields:

```json
{
  "name": "<PERSON>",
  "email": "<EMAIL>",
  "status": "New",
  "last_contacted": "2024-01-15T10:30:00.000Z",
  "id": "507f1f77bcf86cd799439011",
  "created_at": "2024-01-01T00:00:00.000Z"
}
```

## Lead Status Values

- `New` - Newly created lead
- `Contacted` - Lead has been contacted

## Special Endpoints

### Mark Lead as Contacted
```bash
POST /leads/:id/contact
```

### Send Lifetime Offer Email
```bash
POST /leads/:id/send-offer
Content-Type: application/json

{
  "offerUrl": "https://example.com/offer",
  "unsubscribeUrl": "https://example.com/unsubscribe"
}
```

## Benefits of snake_case Consistency

- **Simplicity**: No field name conversion needed
- **Consistency**: Database and API use the same field names
- **Maintainability**: Easier to debug and maintain
- **Performance**: No overhead from field transformations
